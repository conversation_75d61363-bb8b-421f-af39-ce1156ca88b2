<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代转盘抽奖工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .wheel-container {
            position: relative;
            display: inline-block;
        }
        
        .wheel-pointer {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 25px solid #ef4444;
            z-index: 10;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        
        .wheel-canvas {
            border-radius: 50%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.1s ease;
        }
        
        .spinning {
            animation: spin var(--spin-duration, 3s) cubic-bezier(0.23, 1, 0.32, 1) forwards;
        }
        
        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(var(--spin-rotation, 1800deg));
            }
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .prize-item, .user-item, .history-item {
            transition: all 0.2s ease;
        }
        
        .prize-item:hover, .user-item:hover, .history-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            transition: all 0.2s ease;
        }
        
        .btn-secondary:hover {
            transform: translateY(-1px);
        }
        
        .result-modal {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white/80 backdrop-blur-md shadow-sm sticky top-0 z-50">
        <div class="max-w-6xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">转盘抽奖工具</h1>
                        <p class="text-sm text-gray-600 mt-1">现代化的抽奖管理系统</p>
                    </div>
                </div>
                <div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
                    <button onclick="switchTab('wheel')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 bg-white text-blue-600 shadow-sm flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>转盘抽奖</span>
                    </button>
                    <button onclick="switchTab('prizes')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-800 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                        </svg>
                        <span>奖项管理</span>
                    </button>
                    <button onclick="switchTab('users')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-800 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <span>用户管理</span>
                    </button>
                    <button onclick="switchTab('history')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 text-gray-600 hover:text-gray-800 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <span>历史记录</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-6xl mx-auto px-4 py-8">
        <!-- 转盘抽奖页面 -->
        <div id="wheel-tab" class="tab-content active">
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- 转盘区域 -->
                <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-800">开始抽奖</h2>
                    </div>
                    
                    <div class="wheel-container mb-8">
                        <div class="wheel-pointer"></div>
                        <canvas id="wheelCanvas" width="300" height="300" class="wheel-canvas"></canvas>
                    </div>
                    
                    <button id="spinBtn" onclick="startSpin()" class="btn-primary text-white px-8 py-3 rounded-xl font-semibold text-lg mb-4 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>开始抽奖</span>
                    </button>
                    
                    <div id="currentUser" class="text-sm text-gray-600 mb-4"></div>
                    
                    <!-- 中奖结果显示 -->
                    <div id="resultDisplay" class="hidden bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 mt-4">
                        <div class="text-lg font-semibold text-orange-800" id="winnerText"></div>
                        <div class="text-sm text-orange-600 mt-1" id="prizeText"></div>
                        <div class="mt-3 space-x-2">
                            <button onclick="confirmResult()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>确认中奖</span>
                            </button>
                            <button onclick="rejectResult()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span>重新抽奖</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 信息面板 -->
                <div class="space-y-6">
                    <!-- 参与用户 -->
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <div class="flex items-center space-x-2 mb-4">
                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-800">参与用户 (<span id="userCount">0</span>)</h3>
                        </div>
                        <div id="usersList" class="space-y-2 max-h-32 overflow-y-auto"></div>
                    </div>
                    
                    <!-- 奖项列表 -->
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <div class="flex items-center space-x-2 mb-4">
                            <svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-800">当前奖项 (<span id="prizeCount">0</span>)</h3>
                        </div>
                        <div id="prizesList" class="space-y-2 max-h-40 overflow-y-auto"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 奖项管理页面 -->
        <div id="prizes-tab" class="tab-content">
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- 添加奖项表单 -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-800">添加新奖项</h2>
                    </div>
                    <form id="prizeForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">奖项名称</label>
                            <input type="text" id="prizeName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="例如：一等奖">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">中奖概率 (%)</label>
                            <input type="number" id="prizeProbability" required min="0" max="100" step="0.1" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="例如：10">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">奖品数量</label>
                            <input type="number" id="prizeQuantity" required min="1" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="例如：5">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">奖项颜色</label>
                            <input type="color" id="prizeColor" class="w-full h-12 border border-gray-300 rounded-lg cursor-pointer">
                        </div>
                        <button type="submit" class="w-full btn-primary text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all flex items-center justify-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                            </svg>
                            <span>添加奖项</span>
                        </button>
                    </form>
                </div>
                
                <!-- 奖项列表 -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-800">奖项列表</h2>
                    </div>
                    <div id="prizesManageList" class="space-y-3"></div>
                </div>
            </div>
        </div>

        <!-- 用户管理页面 -->
        <div id="users-tab" class="tab-content">
            <div class="grid lg:grid-cols-2 gap-8">
                <!-- 添加用户表单 -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h2 class="text-xl font-semibold text-gray-800">添加参与用户</h2>
                    </div>
                    <form id="userForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">用户姓名</label>
                            <input type="text" id="userName" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="请输入用户姓名">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">联系方式 (可选)</label>
                            <input type="text" id="userContact" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="手机号或邮箱">
                        </div>
                        <button type="submit" class="w-full btn-primary text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all flex items-center justify-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            <span>添加用户</span>
                        </button>
                    </form>
                    
                    <!-- 批量导入 -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="flex items-center space-x-2 mb-3">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-800">批量导入用户</h3>
                        </div>
                        <textarea id="batchUsers" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="每行一个用户名，例如：&#10;张三&#10;李四&#10;王五"></textarea>
                        <button onclick="batchAddUsers()" class="w-full mt-3 bg-green-500 hover:bg-green-600 text-white py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            <span>批量导入</span>
                        </button>
                    </div>
                </div>
                
                <!-- 用户列表 -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h2 class="text-xl font-semibold text-gray-800">用户列表</h2>
                        </div>
                        <button onclick="clearAllUsers()" class="text-red-500 hover:text-red-700 text-sm font-medium transition-colors flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            <span>清空所有</span>
                        </button>
                    </div>
                    <div id="usersManageList" class="space-y-3"></div>
                </div>
            </div>
        </div>

        <!-- 历史记录页面 -->
        <div id="history-tab" class="tab-content">
            <div class="bg-white rounded-2xl shadow-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <h2 class="text-xl font-semibold text-gray-800">抽奖历史记录</h2>
                    </div>
                    <button onclick="clearHistory()" class="text-red-500 hover:text-red-700 text-sm font-medium transition-colors flex items-center space-x-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <span>清空历史</span>
                    </button>
                </div>
                <div id="historyList" class="space-y-3"></div>
            </div>
        </div>
    </main>

    <script>
        // 全局变量
        let prizes = [];
        let users = [];
        let history = [];
        let isSpinning = false;
        let currentWinner = null;
        let currentPrize = null;

        // 初始化应用
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            drawWheel();
            updateUI();
            
            // 绑定表单事件
            document.getElementById('prizeForm').addEventListener('submit', addPrize);
            document.getElementById('userForm').addEventListener('submit', addUser);
        });

        // 数据持久化
        function saveData() {
            localStorage.setItem('lotteryPrizes', JSON.stringify(prizes));
            localStorage.setItem('lotteryUsers', JSON.stringify(users));
            localStorage.setItem('lotteryHistory', JSON.stringify(history));
        }

        function loadData() {
            const savedPrizes = localStorage.getItem('lotteryPrizes');
            const savedUsers = localStorage.getItem('lotteryUsers');
            const savedHistory = localStorage.getItem('lotteryHistory');
            
            if (savedPrizes) prizes = JSON.parse(savedPrizes);
            if (savedUsers) users = JSON.parse(savedUsers);
            if (savedHistory) history = JSON.parse(savedHistory);
            
            // 如果没有数据，添加示例数据
            if (prizes.length === 0) {
                prizes = [
                    { id: 1, name: '一等奖', probability: 5, quantity: 1, color: '#ff6b6b', originalQuantity: 1 },
                    { id: 2, name: '二等奖', probability: 10, quantity: 2, color: '#4ecdc4', originalQuantity: 2 },
                    { id: 3, name: '三等奖', probability: 20, quantity: 5, color: '#45b7d1', originalQuantity: 5 },
                    { id: 4, name: '参与奖', probability: 65, quantity: 20, color: '#96ceb4', originalQuantity: 20 }
                ];
            }
            
            if (users.length === 0) {
                users = [
                    { id: 1, name: '张三', contact: '' },
                    { id: 2, name: '李四', contact: '' },
                    { id: 3, name: '王五', contact: '' },
                    { id: 4, name: '赵六', contact: '' }
                ];
            }
        }

        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 重置所有标签按钮样式
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('bg-white', 'text-blue-600', 'shadow-sm');
                btn.classList.add('text-gray-600', 'hover:text-gray-800');
            });

            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 高亮选中的标签按钮
            event.target.classList.remove('text-gray-600', 'hover:text-gray-800');
            event.target.classList.add('bg-white', 'text-blue-600', 'shadow-sm');

            updateUI();
        }

        // 绘制转盘
        function drawWheel() {
            const canvas = document.getElementById('wheelCanvas');
            const ctx = canvas.getContext('2d');
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 140;

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (prizes.length === 0) {
                // 绘制空转盘
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                ctx.fillStyle = '#f3f4f6';
                ctx.fill();
                ctx.strokeStyle = '#d1d5db';
                ctx.lineWidth = 2;
                ctx.stroke();

                // 添加提示文字
                ctx.fillStyle = '#6b7280';
                ctx.font = '16px Inter';
                ctx.textAlign = 'center';
                ctx.fillText('请先添加奖项', centerX, centerY);
                return;
            }

            // 计算每个奖项的角度
            const totalProbability = prizes.reduce((sum, prize) => sum + prize.probability, 0);
            let currentAngle = 0;

            prizes.forEach((prize, index) => {
                const sliceAngle = (prize.probability / totalProbability) * 2 * Math.PI;

                // 绘制扇形
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = prize.color;
                ctx.fill();
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.stroke();

                // 绘制文字
                const textAngle = currentAngle + sliceAngle / 2;
                const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
                const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

                ctx.save();
                ctx.translate(textX, textY);
                ctx.rotate(textAngle + Math.PI / 2);
                ctx.fillStyle = '#ffffff';
                ctx.font = 'bold 14px Inter';
                ctx.textAlign = 'center';
                ctx.fillText(prize.name, 0, 0);
                ctx.fillStyle = '#ffffff';
                ctx.font = '12px Inter';
                ctx.fillText(`${prize.quantity}个`, 0, 16);
                ctx.restore();

                currentAngle += sliceAngle;
            });

            // 绘制中心圆
            ctx.beginPath();
            ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
            ctx.fillStyle = '#ffffff';
            ctx.fill();
            ctx.strokeStyle = '#d1d5db';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // 开始转盘
        function startSpin() {
            if (isSpinning || users.length === 0 || prizes.length === 0) {
                if (users.length === 0) {
                    alert('请先添加参与用户！');
                } else if (prizes.length === 0) {
                    alert('请先添加奖项！');
                }
                return;
            }

            // 检查是否还有奖品
            const availablePrizes = prizes.filter(prize => prize.quantity > 0);
            if (availablePrizes.length === 0) {
                alert('所有奖品已抽完！');
                return;
            }

            isSpinning = true;
            document.getElementById('spinBtn').disabled = true;
            document.getElementById('resultDisplay').classList.add('hidden');

            // 随机选择用户
            const randomUser = users[Math.floor(Math.random() * users.length)];
            currentWinner = randomUser;

            // 根据概率选择奖项
            currentPrize = selectPrizeByProbability();

            // 计算转盘应该停止的角度
            const prizeIndex = prizes.findIndex(p => p.id === currentPrize.id);
            const totalProbability = prizes.reduce((sum, prize) => sum + prize.probability, 0);
            let targetAngle = 0;

            for (let i = 0; i < prizeIndex; i++) {
                targetAngle += (prizes[i].probability / totalProbability) * 360;
            }
            targetAngle += (prizes[prizeIndex].probability / totalProbability) * 360 / 2;

            // 添加随机旋转圈数
            const randomSpins = 5 + Math.random() * 3;
            const finalRotation = randomSpins * 360 + (360 - targetAngle);

            // 执行旋转动画
            const canvas = document.getElementById('wheelCanvas');
            canvas.style.setProperty('--spin-rotation', finalRotation + 'deg');
            canvas.style.setProperty('--spin-duration', '3s');
            canvas.classList.add('spinning');

            // 动画结束后显示结果
            setTimeout(() => {
                canvas.classList.remove('spinning');
                showResult();
                isSpinning = false;
                document.getElementById('spinBtn').disabled = false;
            }, 3000);
        }

        // 根据概率选择奖项
        function selectPrizeByProbability() {
            const availablePrizes = prizes.filter(prize => prize.quantity > 0);
            const totalProbability = availablePrizes.reduce((sum, prize) => sum + prize.probability, 0);
            const random = Math.random() * totalProbability;

            let currentSum = 0;
            for (const prize of availablePrizes) {
                currentSum += prize.probability;
                if (random <= currentSum) {
                    return prize;
                }
            }

            return availablePrizes[availablePrizes.length - 1];
        }

        // 显示抽奖结果
        function showResult() {
            document.getElementById('winnerText').textContent = `🎉 恭喜 ${currentWinner.name} 中奖！`;
            document.getElementById('prizeText').textContent = `获得：${currentPrize.name}`;
            document.getElementById('resultDisplay').classList.remove('hidden');
            document.getElementById('resultDisplay').classList.add('fade-in');
        }

        // 确认中奖结果
        function confirmResult() {
            if (!currentWinner || !currentPrize) return;

            // 减少奖品数量
            const prizeIndex = prizes.findIndex(p => p.id === currentPrize.id);
            if (prizeIndex !== -1) {
                prizes[prizeIndex].quantity--;
            }

            // 添加到历史记录
            history.unshift({
                id: Date.now(),
                winner: currentWinner.name,
                prize: currentPrize.name,
                time: new Date().toLocaleString(),
                contact: currentWinner.contact || ''
            });

            // 保存数据并更新UI
            saveData();
            updateUI();
            drawWheel();

            // 隐藏结果显示
            document.getElementById('resultDisplay').classList.add('hidden');

            // 重置当前中奖信息
            currentWinner = null;
            currentPrize = null;

            // 显示成功提示
            showNotification('中奖结果已确认！', 'success');
        }

        // 拒绝结果，重新抽奖
        function rejectResult() {
            document.getElementById('resultDisplay').classList.add('hidden');
            currentWinner = null;
            currentPrize = null;

            // 延迟一秒后自动重新开始
            setTimeout(() => {
                startSpin();
            }, 1000);
        }

        // 奖项管理
        function addPrize(event) {
            event.preventDefault();

            const name = document.getElementById('prizeName').value.trim();
            const probability = parseFloat(document.getElementById('prizeProbability').value);
            const quantity = parseInt(document.getElementById('prizeQuantity').value);
            const color = document.getElementById('prizeColor').value;

            if (!name || probability <= 0 || quantity <= 0) {
                alert('请填写有效的奖项信息！');
                return;
            }

            const newPrize = {
                id: Date.now(),
                name,
                probability,
                quantity,
                color,
                originalQuantity: quantity
            };

            prizes.push(newPrize);
            saveData();
            updateUI();
            drawWheel();

            // 重置表单
            document.getElementById('prizeForm').reset();
            document.getElementById('prizeColor').value = getRandomColor();

            showNotification('奖项添加成功！', 'success');
        }

        function editPrize(id) {
            const prize = prizes.find(p => p.id === id);
            if (!prize) return;

            const newName = prompt('请输入新的奖项名称：', prize.name);
            if (newName === null) return;

            const newProbability = prompt('请输入新的中奖概率（%）：', prize.probability);
            if (newProbability === null) return;

            const newQuantity = prompt('请输入新的奖品数量：', prize.quantity);
            if (newQuantity === null) return;

            if (newName.trim() && parseFloat(newProbability) > 0 && parseInt(newQuantity) > 0) {
                prize.name = newName.trim();
                prize.probability = parseFloat(newProbability);
                prize.quantity = parseInt(newQuantity);
                prize.originalQuantity = parseInt(newQuantity);

                saveData();
                updateUI();
                drawWheel();
                showNotification('奖项修改成功！', 'success');
            } else {
                alert('请输入有效的奖项信息！');
            }
        }

        function deletePrize(id) {
            if (confirm('确定要删除这个奖项吗？')) {
                prizes = prizes.filter(p => p.id !== id);
                saveData();
                updateUI();
                drawWheel();
                showNotification('奖项删除成功！', 'success');
            }
        }

        // 用户管理
        function addUser(event) {
            event.preventDefault();

            const name = document.getElementById('userName').value.trim();
            const contact = document.getElementById('userContact').value.trim();

            if (!name) {
                alert('请输入用户姓名！');
                return;
            }

            // 检查是否已存在
            if (users.some(user => user.name === name)) {
                alert('该用户已存在！');
                return;
            }

            const newUser = {
                id: Date.now(),
                name,
                contact
            };

            users.push(newUser);
            saveData();
            updateUI();

            // 重置表单
            document.getElementById('userForm').reset();

            showNotification('用户添加成功！', 'success');
        }

        function deleteUser(id) {
            if (confirm('确定要删除这个用户吗？')) {
                users = users.filter(u => u.id !== id);
                saveData();
                updateUI();
                showNotification('用户删除成功！', 'success');
            }
        }

        function batchAddUsers() {
            const batchText = document.getElementById('batchUsers').value.trim();
            if (!batchText) {
                alert('请输入用户名列表！');
                return;
            }

            const userNames = batchText.split('\n').map(name => name.trim()).filter(name => name);
            let addedCount = 0;

            userNames.forEach(name => {
                if (!users.some(user => user.name === name)) {
                    users.push({
                        id: Date.now() + Math.random(),
                        name,
                        contact: ''
                    });
                    addedCount++;
                }
            });

            if (addedCount > 0) {
                saveData();
                updateUI();
                document.getElementById('batchUsers').value = '';
                showNotification(`成功添加 ${addedCount} 个用户！`, 'success');
            } else {
                alert('没有新用户被添加（可能已存在）！');
            }
        }

        function clearAllUsers() {
            if (confirm('确定要清空所有用户吗？')) {
                users = [];
                saveData();
                updateUI();
                showNotification('所有用户已清空！', 'success');
            }
        }

        // 历史记录管理
        function clearHistory() {
            if (confirm('确定要清空所有历史记录吗？')) {
                history = [];
                saveData();
                updateUI();
                showNotification('历史记录已清空！', 'success');
            }
        }

        // 更新UI
        function updateUI() {
            updateUsersList();
            updatePrizesList();
            updatePrizesManageList();
            updateUsersManageList();
            updateHistoryList();
            updateCounts();
        }

        function updateUsersList() {
            const container = document.getElementById('usersList');
            if (users.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-sm text-center py-4">暂无参与用户</div>';
                return;
            }

            container.innerHTML = users.slice(0, 5).map(user => `
                <div class="flex items-center justify-between bg-gray-50 rounded-lg px-3 py-2">
                    <span class="text-sm font-medium text-gray-700">${user.name}</span>
                    <span class="text-xs text-gray-500">${user.contact || '无联系方式'}</span>
                </div>
            `).join('') + (users.length > 5 ? `<div class="text-xs text-gray-500 text-center">还有 ${users.length - 5} 个用户...</div>` : '');
        }

        function updatePrizesList() {
            const container = document.getElementById('prizesList');
            if (prizes.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-sm text-center py-4">暂无奖项</div>';
                return;
            }

            container.innerHTML = prizes.map(prize => `
                <div class="flex items-center justify-between bg-gray-50 rounded-lg px-3 py-2">
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded-full" style="background-color: ${prize.color}"></div>
                        <span class="text-sm font-medium text-gray-700">${prize.name}</span>
                    </div>
                    <div class="text-xs text-gray-500">
                        ${prize.quantity}/${prize.originalQuantity} | ${prize.probability}%
                    </div>
                </div>
            `).join('');
        }

        function updatePrizesManageList() {
            const container = document.getElementById('prizesManageList');
            if (prizes.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-8">暂无奖项，请添加奖项</div>';
                return;
            }

            container.innerHTML = prizes.map(prize => `
                <div class="prize-item bg-gray-50 rounded-xl p-4 border border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 rounded-full" style="background-color: ${prize.color}"></div>
                            <h3 class="font-semibold text-gray-800">${prize.name}</h3>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="editPrize(${prize.id})" class="text-blue-500 hover:text-blue-700 text-sm flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>编辑</span>
                            </button>
                            <button onclick="deletePrize(${prize.id})" class="text-red-500 hover:text-red-700 text-sm flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                <span>删除</span>
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>概率: <span class="font-medium">${prize.probability}%</span></div>
                        <div>数量: <span class="font-medium">${prize.quantity}/${prize.originalQuantity}</span></div>
                        <div>状态: <span class="font-medium ${prize.quantity > 0 ? 'text-green-600' : 'text-red-600'}">${prize.quantity > 0 ? '可用' : '已完'}</span></div>
                    </div>
                </div>
            `).join('');
        }

        function updateUsersManageList() {
            const container = document.getElementById('usersManageList');
            if (users.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-8">暂无用户，请添加用户</div>';
                return;
            }

            container.innerHTML = users.map(user => `
                <div class="user-item bg-gray-50 rounded-xl p-4 border border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-gray-800">${user.name}</h3>
                            <p class="text-sm text-gray-600">${user.contact || '无联系方式'}</p>
                        </div>
                        <button onclick="deleteUser(${user.id})" class="text-red-500 hover:text-red-700 text-sm flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            <span>删除</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function updateHistoryList() {
            const container = document.getElementById('historyList');
            if (history.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-8">暂无抽奖记录</div>';
                return;
            }

            container.innerHTML = history.map(record => `
                <div class="history-item bg-gray-50 rounded-xl p-4 border border-gray-200">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${record.winner}</h3>
                                <p class="text-sm text-gray-600">获得：${record.prize}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-500">${record.time}</div>
                            ${record.contact ? `<div class="text-xs text-gray-400">${record.contact}</div>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateCounts() {
            document.getElementById('userCount').textContent = users.length;
            document.getElementById('prizeCount').textContent = prizes.length;

            // 更新当前用户显示
            const currentUserDiv = document.getElementById('currentUser');
            if (users.length > 0) {
                currentUserDiv.textContent = `当前有 ${users.length} 位用户参与抽奖`;
            } else {
                currentUserDiv.textContent = '请先添加参与用户';
            }
        }

        // 工具函数
        function getRandomColor() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd'];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transition-all duration-300 transform translate-x-full`;

            // 根据类型设置颜色
            switch (type) {
                case 'success':
                    notification.classList.add('bg-green-500');
                    break;
                case 'error':
                    notification.classList.add('bg-red-500');
                    break;
                default:
                    notification.classList.add('bg-blue-500');
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 初始化颜色选择器
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('prizeColor').value = getRandomColor();
        });
    </script>
</body>
</html>
